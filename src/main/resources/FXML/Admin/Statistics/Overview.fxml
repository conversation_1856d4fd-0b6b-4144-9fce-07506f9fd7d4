<?xml version="1.0" encoding="UTF-8"?>

<?import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView?>
<?import java.lang.String?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ChoiceBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Pane?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.shape.Line?>

<?import javafx.scene.chart.BarChart?>
<?import javafx.scene.chart.CategoryAxis?>
<?import javafx.scene.chart.NumberAxis?>
<?import javafx.collections.FXCollections?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.geometry.Insets?>
<?import javafx.scene.control.DatePicker?>
<?import javafx.scene.control.ProgressBar?>
<VBox spacing="20.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0"
      AnchorPane.topAnchor="0.0"
      maxHeight="Infinity" maxWidth="Infinity" minHeight="-Infinity" minWidth="-Infinity" styleClass="root"
      stylesheets="@../../../Styles/Statistics.css" xmlns="http://javafx.com/javafx/23.0.1"
      xmlns:fx="http://javafx.com/fxml/1"
      fx:controller="com.store.app.petstore.Controllers.Admin.Statistic.OverViewController"
      alignment="CENTER">
    <fx:include source="../AdminMenu.fxml"/>
    <HBox styleClass="hbox-statistic" spacing="20" HBox.hgrow="ALWAYS">
        <padding>
            <Insets top="20"/>
        </padding>
        <HBox styleClass="statistic-item-tag revenue" HBox.hgrow="ALWAYS" minWidth="250" maxWidth="500">
            <Pane prefHeight="50.0" prefWidth="50.0">
                <FontAwesomeIconView glyphName="DOLLAR" layoutX="8.0" layoutY="38.0" size="40"
                                     styleClass="item-tag-icon"/>
            </Pane>
            <VBox prefHeight="100.0" maxWidth="400" spacing="5">
                <Label text="TỔNG DOANH THU" styleClass="item-tag-title">
                    <graphic>
                        <Line endX="100" stroke="#01C4DA" strokeWidth="2"/>
                    </graphic>
                </Label>
                <HBox alignment="CENTER_LEFT" spacing="5">
                    <Label fx:id="totalRevenueLabel" text="0.00" styleClass="item-tag-number"/>
                    <Label text="VND" styleClass="item-tag-unit"/>
                </HBox>
                <Label text="Hôm nay, 04/06/2025" styleClass="item-tag-date"/>
            </VBox>
        </HBox>
        <HBox styleClass="statistic-item-tag" HBox.hgrow="ALWAYS" minWidth="200" maxWidth="Infinity">
            <Pane prefHeight="35.0" prefWidth="35.0">
                <FontAwesomeIconView glyphName="PAW" layoutX="-4.0" layoutY="28.0" size="35"
                                     styleClass="item-tag-icon"/>
            </Pane>
            <VBox prefHeight="50.0" maxWidth="Infinity">
                <Label text="Tổng thú cưng đã bán trong ngày:">
                    <styleClass>
                        <String fx:value="item-tag-text"/>
                        <String fx:value="title"/>
                    </styleClass>
                </Label>
                <HBox prefHeight="100.0" prefWidth="200.0">
                    <children>
                        <Label fx:id="totalPetsSoldLabel" prefHeight="17.0" prefWidth="141.0" text="0">
                            <styleClass>
                                <String fx:value="item-tag-text"/>
                                <String fx:value="number"/>
                            </styleClass>
                        </Label>
                        <Label text="con">
                            <styleClass>
                                <String fx:value="item-tag-text"/>
                                <String fx:value="number"/>
                            </styleClass>
                        </Label>
                    </children>
                </HBox>
                <VBox alignment="CENTER_RIGHT" prefHeight="200.0" prefWidth="100.0">
                    <Label text="04/06/2025" textAlignment="RIGHT">
                        <styleClass>
                            <String fx:value="item-tag-text"/>
                            <String fx:value="date"/>
                        </styleClass>
                    </Label>
                </VBox>
            </VBox>
        </HBox>
        <HBox styleClass="statistic-item-tag" HBox.hgrow="ALWAYS" minWidth="200" maxWidth="Infinity">
            <Pane prefHeight="35.0" prefWidth="35.0">
                <FontAwesomeIconView glyphName="ARCHIVE" layoutX="-4.0" layoutY="29.0" size="35"
                                     styleClass="item-tag-icon"/>
            </Pane>
            <VBox prefHeight="50.0" maxWidth="Infinity">
                <Label text="Tổng sản phẩm đã bán trong ngày:">
                    <styleClass>
                        <String fx:value="item-tag-text"/>
                        <String fx:value="title"/>
                    </styleClass>
                </Label>
                <HBox prefHeight="100.0" prefWidth="200.0">
                    <Label fx:id="totalProductsSoldLabel" prefHeight="17.0" prefWidth="141.0" text="0">
                        <styleClass>
                            <String fx:value="item-tag-text"/>
                            <String fx:value="number"/>
                        </styleClass>
                    </Label>
                    <Label prefHeight="17.0" prefWidth="89.0" text="sản phẩm">
                        <styleClass>
                            <String fx:value="item-tag-text"/>
                            <String fx:value="number"/>
                        </styleClass>
                    </Label>
                </HBox>
                <VBox alignment="CENTER_RIGHT" prefHeight="200.0" prefWidth="100.0">
                    <Label text="04/06/2025" textAlignment="RIGHT">
                        <styleClass>
                            <String fx:value="item-tag-text"/>
                            <String fx:value="date"/>
                        </styleClass>
                    </Label>
                </VBox>
            </VBox>
        </HBox>
        <HBox styleClass="statistic-item-tag" HBox.hgrow="ALWAYS" minWidth="200" maxWidth="400">
            <Pane prefHeight="35.0" prefWidth="35.0">
                <FontAwesomeIconView glyphName="CALCULATOR" layoutX="-4.0" layoutY="29.0" size="35"
                                     styleClass="item-tag-icon"/>
            </Pane>
            <VBox prefHeight="50.0" maxWidth="Infinity">
                <Label text="Tổng hóa đơn trong ngày:">
                    <styleClass>
                        <String fx:value="item-tag-text"/>
                        <String fx:value="title"/>
                    </styleClass>
                </Label>
                <HBox prefHeight="100.0" prefWidth="200.0">
                    <Label fx:id="totalInvoicesLabel" prefHeight="17.0" prefWidth="121.0" text="0">
                        <styleClass>
                            <String fx:value="item-tag-text"/>
                            <String fx:value="number"/>
                        </styleClass>
                    </Label>
                    <Label prefHeight="17.0" prefWidth="53.0" text="hóa đơn">
                        <styleClass>
                            <String fx:value="item-tag-text"/>
                            <String fx:value="number"/>
                        </styleClass>
                    </Label>
                </HBox>
                <VBox alignment="CENTER_RIGHT" prefHeight="200.0" prefWidth="100.0">
                    <Label text="04/06/2025" textAlignment="RIGHT">
                        <styleClass>
                            <String fx:value="item-tag-text"/>
                            <String fx:value="date"/>
                        </styleClass>
                    </Label>
                </VBox>
            </VBox>
        </HBox>
    </HBox>
    <HBox spacing="10.0" HBox.hgrow="ALWAYS" VBox.vgrow="ALWAYS">
        <padding>
            <Insets top="20"/>
        </padding>
        <VBox styleClass="main-statistic-tag" VBox.vgrow="ALWAYS" minWidth="300" maxWidth="Infinity"
              HBox.hgrow="ALWAYS">
            <HBox spacing="10.0" prefHeight="50">
                <Label prefHeight="29.0" prefWidth="310.0" styleClass="main-statistic-text"
                       text="Biểu đồ thống kê doanh thu"/>
                <DatePicker fx:id="startDatePicker" prefHeight="30.0" prefWidth="110.0">
                    <styleClass>
                        <String fx:value="choice-box-filter"/>
                        <String fx:value="yellow"/>
                    </styleClass>
                </DatePicker>
                <DatePicker fx:id="endDatePicker" prefHeight="30.0" prefWidth="110.0">
                    <styleClass>
                        <String fx:value="choice-box-filter"/>
                        <String fx:value="yellow"/>
                    </styleClass>
                </DatePicker>
                <Button fx:id="viewRevenueButton" text="Xem" prefHeight="30.0" styleClass="view-button"
                        onAction="#onViewRevenueButtonClicked"/>
            </HBox>
            <ProgressBar fx:id="chartProgressBar" layoutX="10.0" layoutY="60.0" prefWidth="200.0" visible="false"/>
            <BarChart fx:id="summaryChart" title="Tổng quan doanh thu" legendVisible="false" animated="false"
                      minHeight="400" maxHeight="Infinity" maxWidth="Infinity">
                <xAxis>
                    <CategoryAxis label="Tháng"/>
                </xAxis>
                <yAxis>
                    <NumberAxis label="Doanh thu (triệu VND)"/>
                </yAxis>
            </BarChart>
        </VBox>
        <VBox styleClass="main-statistic-tag" VBox.vgrow="ALWAYS" minWidth="200" maxWidth="Infinity"
              HBox.hgrow="ALWAYS">
            <HBox maxWidth="Infinity" spacing="10">
                <padding>
                    <Insets bottom="20" right="10"/>
                </padding>
                <Label prefHeight="21.0" prefWidth="270.0" styleClass="main-statistic-text"
                       text="Tổng hóa đơn gần đây"/>
                <ChoiceBox prefHeight="30.0" prefWidth="160">
                    <styleClass>
                        <String fx:value="choice-box-filter"/>
                        <String fx:value="yellow"/>
                    </styleClass>
                    <items>
                        <FXCollections fx:factory="observableArrayList">
                            <String fx:value="Tháng 1"/>
                            <String fx:value="Tháng 2"/>
                            <String fx:value="Tháng 3"/>
                            <String fx:value="Tháng 4"/>
                            <String fx:value="Tháng 5"/>
                            <String fx:value="Tháng 6"/>
                            <String fx:value="Tháng 7"/>
                            <String fx:value="Tháng 8"/>
                            <String fx:value="Tháng 9"/>
                            <String fx:value="Tháng 10"/>
                            <String fx:value="Tháng 11"/>
                            <String fx:value="Tháng 12"/>
                        </FXCollections>
                    </items>
                </ChoiceBox>
                <Button fx:id="viewRecentOrdersButton" text="Xem" prefHeight="30.0" styleClass="view-button"
                        onAction="#onViewRecentOrdersButtonClicked"/>
            </HBox>
            <ProgressBar fx:id="tableProgressBar" layoutX="10.0" layoutY="60.0" prefWidth="200.0" visible="false"/>
            <TableView fx:id="recentOrdersTable" minHeight="400" HBox.hgrow="ALWAYS" minWidth="600"
                       styleClass="table-view">
                <columnResizePolicy>
                    <TableView fx:constant="CONSTRAINED_RESIZE_POLICY"/>
                </columnResizePolicy>
                <columns>
                    <TableColumn text="Mã HD" prefWidth="100"/>
                    <TableColumn text="Khách hàng" prefWidth="150"/>
                    <TableColumn text="Ngày" prefWidth="100"/>
                    <TableColumn text="Tổng tiền" prefWidth="100"/>
                </columns>
            </TableView>
        </VBox>
    </HBox>
</VBox>
