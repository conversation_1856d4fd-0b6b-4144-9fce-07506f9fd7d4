<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.DatePicker?>
<?import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView?>
<?import java.lang.String?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ChoiceBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Pane?>
<?import javafx.scene.shape.Line?>
<?import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView?>
<?import java.lang.String?>
<?import javafx.scene.control.ChoiceBox?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Pane?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.shape.Line?>

<?import javafx.scene.chart.BarChart?>
<?import javafx.scene.chart.CategoryAxis?>
<?import javafx.scene.chart.NumberAxis?>
<?import javafx.collections.FXCollections?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.control.cell.PropertyValueFactory?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.geometry.Insets?>

<?import javafx.scene.control.ProgressBar?>
<AnchorPane styleClass="root"
            stylesheets="@../../../Styles/Statistics.css"
            xmlns="http://javafx.com/javafx/23.0.1"
            xmlns:fx="http://javafx.com/fxml/1"
            fx:controller="com.store.app.petstore.Controllers.Admin.Statistic.RevenueController">

    <VBox spacing="20.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0"
          AnchorPane.topAnchor="0.0" alignment="CENTER">
        <fx:include source="../AdminMenu.fxml"/>
        <HBox styleClass="hbox-statistic" spacing="20" AnchorPane.topAnchor="20" AnchorPane.leftAnchor="20"
              AnchorPane.rightAnchor="20">
            <HBox styleClass="statistic-item-tag revenue" HBox.hgrow="ALWAYS" minWidth="250">
                <Pane prefHeight="50.0" prefWidth="50.0">
                    <FontAwesomeIconView glyphName="DOLLAR" layoutX="8.0" layoutY="38.0" size="40"
                                         styleClass="item-tag-icon"/>
                </Pane>
                <VBox spacing="5" HBox.hgrow="ALWAYS">
                    <Label text="TỔNG DOANH THU" styleClass="item-tag-title">
                        <graphic>
                            <Line endX="100" stroke="#01C4DA" strokeWidth="2"/>
                        </graphic>
                    </Label>
                    <HBox alignment="CENTER_LEFT" spacing="5">
                        <Label fx:id="totalRevenueValueLabel" text="0.00" styleClass="item-tag-number"/>
                        <Label text="VND" styleClass="item-tag-unit"/>
                    </HBox>
                    <Label text="Hôm nay, 04/06/2025" styleClass="item-tag-date"/>
                </VBox>
            </HBox>
            <HBox styleClass="statistic-item-tag" HBox.hgrow="ALWAYS" minWidth="200">
                <Pane prefHeight="35.0" prefWidth="35.0">
                    <FontAwesomeIconView glyphName="PAW" layoutX="-4.0" layoutY="28.0" size="35"
                                         styleClass="item-tag-icon"/>
                </Pane>
                <VBox spacing="5" HBox.hgrow="ALWAYS">
                    <Label text="Tổng doanh thu tháng này:">
                        <styleClass>
                            <String fx:value="item-tag-text"/>
                            <String fx:value="title"/>
                        </styleClass>
                    </Label>
                    <HBox alignment="CENTER_LEFT" spacing="5">
                        <Label fx:id="monthlyRevenueLabel" text="0">
                            <styleClass>
                                <String fx:value="item-tag-text"/>
                                <String fx:value="number"/>
                            </styleClass>
                        </Label>
                        <Label text="VND">
                            <styleClass>
                                <String fx:value="item-tag-text"/>
                                <String fx:value="number"/>
                            </styleClass>
                        </Label>
                    </HBox>
                    <Label text="04/06/2025" textAlignment="RIGHT" HBox.hgrow="ALWAYS">
                        <styleClass>
                            <String fx:value="item-tag-text"/>
                            <String fx:value="date"/>
                        </styleClass>
                    </Label>
                </VBox>
            </HBox>
            <HBox styleClass="statistic-item-tag" HBox.hgrow="ALWAYS" minWidth="200">
                <Pane prefHeight="35.0" prefWidth="35.0">
                    <FontAwesomeIconView glyphName="ARCHIVE" layoutX="-4.0" layoutY="29.0" size="35"
                                         styleClass="item-tag-icon"/>
                </Pane>
                <VBox spacing="5" HBox.hgrow="ALWAYS">
                    <Label text="Tổng doanh thu tháng trước:">
                        <styleClass>
                            <String fx:value="item-tag-text"/>
                            <String fx:value="title"/>
                        </styleClass>
                    </Label>
                    <HBox alignment="CENTER_LEFT" spacing="5">
                        <Label fx:id="lastMonthRevenueLabel" text="0">
                            <styleClass>
                                <String fx:value="item-tag-text"/>
                                <String fx:value="number"/>
                            </styleClass>
                        </Label>
                        <Label text="VND">
                            <styleClass>
                                <String fx:value="item-tag-text"/>
                                <String fx:value="number"/>
                            </styleClass>
                        </Label>
                    </HBox>
                    <Label text="04/06/2025" textAlignment="RIGHT" HBox.hgrow="ALWAYS">
                        <styleClass>
                            <String fx:value="item-tag-text"/>
                            <String fx:value="date"/>
                        </styleClass>
                    </Label>
                </VBox>
            </HBox>
            <HBox styleClass="statistic-item-tag" HBox.hgrow="ALWAYS" minWidth="200">
                <Pane prefHeight="35.0" prefWidth="35.0">
                    <FontAwesomeIconView glyphName="CALCULATOR" layoutX="-4.0" layoutY="29.0" size="35"
                                         styleClass="item-tag-icon"/>
                </Pane>
                <VBox spacing="5" HBox.hgrow="ALWAYS">
                    <Label text="Tỉ lệ doanh thu:">
                        <styleClass>
                            <String fx:value="item-tag-text"/>
                            <String fx:value="title"/>
                        </styleClass>
                    </Label>
                    <HBox alignment="CENTER_LEFT" spacing="5">
                        <Label fx:id="revenueRatioLabel" text="0">
                            <styleClass>
                                <String fx:value="item-tag-text"/>
                                <String fx:value="number"/>
                            </styleClass>
                        </Label>
                        <Label text="\%">
                            <styleClass>
                                <String fx:value="item-tag-text"/>
                                <String fx:value="number"/>
                            </styleClass>
                        </Label>
                    </HBox>
                    <Label text="04/06/2025" textAlignment="RIGHT" HBox.hgrow="ALWAYS">
                        <styleClass>
                            <String fx:value="item-tag-text"/>
                            <String fx:value="date"/>
                        </styleClass>
                    </Label>
                </VBox>
            </HBox>
        </HBox>
        <HBox spacing="20.0" AnchorPane.topAnchor="180" style="-fx-padding: 20px"
              VBox.vgrow="ALWAYS"
              AnchorPane.bottomAnchor="20">
            <VBox styleClass="main-statistic-tag" VBox.vgrow="ALWAYS" HBox.hgrow="ALWAYS">
                <HBox spacing="10.0" prefHeight="50" alignment="CENTER_LEFT">
                    <VBox spacing="10">
                        <Label styleClass="main-statistic-text"
                               text="Biểu đồ thống kê doanh thu"/>
                        <HBox spacing="20" alignment="CENTER_LEFT" HBox.hgrow="ALWAYS">
                            <DatePicker fx:id="revenueChartFilter1" prefHeight="30.0" prefWidth="110.0">
                                <styleClass>
                                    <String fx:value="date-picker-filter"/>
                                    <String fx:value="yellow"/>
                                </styleClass>
                            </DatePicker>
                            <Label text="đến"/>
                            <DatePicker fx:id="revenueChartFilter2" prefHeight="30.0" prefWidth="110.0">
                                <styleClass>
                                    <String fx:value="date-picker-filter"/>
                                    <String fx:value="yellow"/>
                                </styleClass>
                            </DatePicker>
                            <Button fx:id="viewChartButton" text="Xem" prefHeight="30.0" styleClass="view-button"
                                    onAction="#handleViewChartButtonAction"/>
                        </HBox>
                    </VBox>
                </HBox>
                <ProgressBar fx:id="chartProgressBar" prefWidth="200" visible="false" VBox.vgrow="NEVER"/>
                <BarChart fx:id="summaryChart" title="Tổng quan doanh thu" legendVisible="false" animated="false"
                          prefHeight="400" VBox.vgrow="ALWAYS">
                    <xAxis>
                        <CategoryAxis label="Tháng"/>
                    </xAxis>
                    <yAxis>
                        <NumberAxis label="Doanh thu (triệu VND)"/>
                    </yAxis>
                </BarChart>
            </VBox>
            <VBox styleClass="main-statistic-tag" VBox.vgrow="ALWAYS" HBox.hgrow="ALWAYS">
                <HBox alignment="CENTER_LEFT" spacing="10">
                    <padding>
                        <Insets bottom="20"/>
                    </padding>
                    <VBox spacing="10">
                        <Label styleClass="main-statistic-text"
                               text="Doanh thu theo nhân viên"/>
                        <HBox spacing="20">
                            <ChoiceBox fx:id="staffRevenueFilter" prefHeight="30.0" prefWidth="160"
                                        HBox.hgrow="ALWAYS">
                                <styleClass>
                                    <String fx:value="choice-box-filter"/>
                                    <String fx:value="yellow"/>
                                </styleClass>
                                <items>
                                    <FXCollections fx:factory="observableArrayList">
                                        <String fx:value="Tháng 1"/>
                                        <String fx:value="Tháng 2"/>
                                        <String fx:value="Tháng 3"/>
                                        <String fx:value="Tháng 4"/>
                                        <String fx:value="Tháng 5"/>
                                        <String fx:value="Tháng 6"/>
                                        <String fx:value="Tháng 7"/>
                                        <String fx:value="Tháng 8"/>
                                        <String fx:value="Tháng 9"/>
                                        <String fx:value="Tháng 10"/>
                                        <String fx:value="Tháng 11"/>
                                        <String fx:value="Tháng 12"/>
                                    </FXCollections>
                                </items>
                            </ChoiceBox>
                            <Button fx:id="viewStaffButton" text="Xem" prefHeight="30.0" styleClass="view-button"
                                    onAction="#handleViewStaffButtonAction"/>
                        </HBox>

                    </VBox>
                </HBox>
                <ProgressBar fx:id="staffTableProgressBar" prefWidth="200" visible="false" VBox.vgrow="NEVER"/>
                <TableView fx:id="staffRevenueTable" prefHeight="400" VBox.vgrow="ALWAYS"
                           styleClass="table-view">
                    <columnResizePolicy>
                        <TableView fx:constant="CONSTRAINED_RESIZE_POLICY"/>
                    </columnResizePolicy>
                    <columns>
                        <TableColumn text="Tên nhân viên" prefWidth="150">
                            <cellValueFactory>
                                <PropertyValueFactory property="staffName"/>
                            </cellValueFactory>
                        </TableColumn>
                        <TableColumn text="Doanh thu" prefWidth="100">
                            <cellValueFactory>
                                <PropertyValueFactory property="totalRevenue"/>
                            </cellValueFactory>
                        </TableColumn>
                    </columns>
                </TableView>
            </VBox>
        </HBox>
    </VBox>
</AnchorPane>
